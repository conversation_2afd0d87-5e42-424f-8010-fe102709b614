use super::models::*;
use chrono::Utc;
use std::collections::HashMap;

// Training Service - Business Logic Layer
#[derive(Default)]
pub struct TrainingService;

impl TrainingService {
    // Training Needs Management
    pub fn create_training_need(
        &self,
        request: CreateTrainingNeedRequest,
    ) -> Result<TrainingNeed, String> {
        // Validate input
        Self::validate_training_need(&request)?;
        
        // Calculate priority based on skill gaps
        let calculated_priority = Self::calculate_priority(&request.skill_gaps);
        
        let training_need = TrainingNeed {
            id: None,
            employee_id: request.employee_id,
            employee_name: request.employee_name,
            department: request.department,
            position: request.position,
            manager: None,
            hire_date: None,
            location: None,
            current_skills: None,
            required_skills: None,
            skill_gaps: Some(request.skill_gaps),
            priority: calculated_priority,
            training_type: request.training_type,
            recommended_training: request.recommended_training,
            estimated_cost: request.estimated_cost,
            currency: "USD".to_string(),
            expected_roi: request.expected_roi,
            completion_timeline: request.completion_timeline,
            status: TrainingStatus::NotStarted,
            completion_percentage: 0.0,
            assessment_score: None,
            certificate_number: None,
            expiry_date: None,
            notes: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        // Save to database
        Self::save_training_need(training_need)
    }
    
    pub fn get_training_needs(
        &self,
        filter: Option<TrainingNeedFilter>,
    ) -> Result<Vec<TrainingNeed>, String> {
        // Apply filters and fetch from database
        Self::fetch_training_needs(filter)
    }
    
    pub fn update_training_need(
        &self,
        id: i64,
        updates: TrainingNeed,
    ) -> Result<TrainingNeed, String> {
        // Validate updates
        let mut existing = Self::get_training_need_by_id(id)?;
        
        // Merge updates
        existing.updated_at = Utc::now();
        existing.status = updates.status;
        existing.completion_percentage = updates.completion_percentage;
        
        // Save updates
        Self::save_training_need(existing)
    }
    
    pub fn delete_training_need(&self, id: i64) -> Result<bool, String> {
        // Check for dependencies
        if Self::has_active_enrollments(id)? {
            return Err("Cannot delete training need with active enrollments".to_string());
        }
        
        Self::delete_from_db(id)
    }
    
    // Skills Gap Analysis
    pub fn analyze_skills_gap(
        &self,
        request: SkillsGapAnalysisRequest,
    ) -> Result<Vec<SkillsGapAnalysisResult>, String> {
        let mut results = Vec::new();
        
        // Get employees to analyze
        let employees = if let Some(ids) = request.employee_ids {
            Self::get_employees_by_ids(ids)?
        } else if let Some(dept) = request.department {
            Self::get_employees_by_department(dept)?
        } else {
            Self::get_all_employees()?
        };
        
        for employee in employees {
            let gaps = Self::calculate_skill_gaps(&employee)?;
            let overall_gap_score = Self::calculate_overall_gap_score(&gaps);
            
            let mut result = SkillsGapAnalysisResult {
                employee_id: employee.id.clone(),
                employee_name: employee.name.clone(),
                overall_gap_score,
                critical_gaps: gaps.iter()
                    .filter(|g| g.importance == Importance::Critical)
                    .cloned()
                    .collect(),
                strengths: gaps.iter()
                    .filter(|g| g.gap_score <= 0)
                    .cloned()
                    .collect(),
                recommendations: Vec::new(),
                estimated_training_hours: 0.0,
                estimated_cost: 0.0,
                priority: Priority::Medium,
            };
            
            // Generate recommendations if requested
            if request.include_recommendations {
                result.recommendations = Self::generate_recommendations(&gaps)?;
                result.estimated_training_hours = Self::estimate_training_hours(&result.recommendations);
                result.estimated_cost = Self::estimate_total_cost(&result.recommendations);
            }
            
            // Determine priority
            result.priority = Self::determine_priority(overall_gap_score, &gaps);
            
            results.push(result);
        }
        
        Ok(results)
    }
    
    // Training Programs Management
    pub fn create_training_program(
        &self,
        program: TrainingProgram,
    ) -> Result<TrainingProgram, String> {
        // Validate program details
        Self::validate_training_program(&program)?;
        
        // Check for duplicates
        if Self::program_exists(&program.name)? {
            return Err(format!("Training program '{}' already exists", program.name));
        }
        
        Self::save_training_program(program)
    }
    
    pub fn match_programs_to_needs(
        &self,
        need_id: i64,
    ) -> Result<Vec<TrainingProgram>, String> {
        let need = Self::get_training_need_by_id(need_id)?;
        let mut matched_programs = Vec::new();
        
        // Get all programs
        let programs = Self::get_all_programs()?;
        
        for program in programs {
            let match_score = Self::calculate_match_score(&need, &program);
            if match_score > 0.7 {
                matched_programs.push(program);
            }
        }
        
        // Sort by match score
        matched_programs.sort_by(|a, b| {
            Self::calculate_match_score(&need, b)
                .partial_cmp(&Self::calculate_match_score(&need, a))
                .unwrap()
        });
        
        Ok(matched_programs)
    }
    
    // Learning Paths
    pub fn create_learning_path(
        &self,
        employee_id: String,
        target_role: String,
    ) -> Result<LearningPath, String> {
        // Analyze current skills vs target role requirements
        let current_skills = Self::get_employee_skills(&employee_id)?;
        let target_skills = Self::get_role_requirements(&target_role)?;
        
        // Identify skill gaps
        let gaps = Self::identify_gaps(&current_skills, &target_skills);
        
        // Build learning modules
        let modules = Self::build_learning_modules(&gaps)?;
        
        // Calculate totals
        let total_hours: f64 = modules.iter().map(|m| m.duration_hours).sum();
        let total_cost: f64 = Self::calculate_path_cost(&modules)?;
        
        let path = LearningPath {
            id: None,
            employee_id: employee_id.clone(),
            employee_name: Self::get_employee_name(&employee_id)?,
            path_name: format!("Path to {}", target_role),
            description: Some(format!("Learning path for progression to {}", target_role)),
            target_role: Some(target_role),
            modules,
            total_duration_hours: total_hours,
            total_cost,
            currency: "USD".to_string(),
            progress_percentage: 0.0,
            status: PathStatus::NotStarted,
            start_date: Utc::now().format("%Y-%m-%d").to_string(),
            expected_completion: Self::calculate_completion_date(total_hours),
            actual_completion: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        Self::save_learning_path(path)
    }
    
    pub fn update_module_progress(
        &self,
        path_id: i64,
        module_id: String,
        progress: f64,
    ) -> Result<LearningPath, String> {
        let mut path = Self::get_learning_path_by_id(path_id)?;
        
        // Update module progress
        for module in &mut path.modules {
            if module.module_id == module_id {
                module.progress_percentage = progress;
                if progress >= 100.0 {
                    module.status = ModuleStatus::Completed;
                    module.completion_date = Some(Utc::now().format("%Y-%m-%d").to_string());
                } else if progress > 0.0 {
                    module.status = ModuleStatus::InProgress;
                }
                break;
            }
        }
        
        // Recalculate overall progress
        let total_modules = path.modules.len() as f64;
        let completed_modules = path.modules.iter()
            .filter(|m| m.status == ModuleStatus::Completed)
            .count() as f64;
        
        path.progress_percentage = (completed_modules / total_modules) * 100.0;
        
        // Update path status
        if path.progress_percentage >= 100.0 {
            path.status = PathStatus::Completed;
            path.actual_completion = Some(Utc::now().format("%Y-%m-%d").to_string());
        } else if path.progress_percentage > 0.0 {
            path.status = PathStatus::InProgress;
        }
        
        path.updated_at = Utc::now();
        
        Self::save_learning_path(path)
    }
    
    // Analytics and Reporting
    pub fn get_training_metrics(
        &self,
        period_start: String,
        period_end: String,
    ) -> Result<TrainingMetrics, String> {
        // Fetch all relevant data
        let all_needs = Self::fetch_training_needs(None)?;
        let all_programs = Self::get_all_programs()?;
        let all_paths = Self::get_all_learning_paths()?;
        
        // Calculate metrics
        let total_employees = Self::get_total_employee_count()?;
        let employees_in_training = all_paths.iter()
            .filter(|p| p.status == PathStatus::InProgress)
            .count() as i32;
        
        let completed_this_month = all_needs.iter()
            .filter(|n| {
                n.status == TrainingStatus::Completed &&
                n.updated_at.format("%Y-%m").to_string() == Utc::now().format("%Y-%m").to_string()
            })
            .count() as i32;
        
        let total_budget: f64 = all_needs.iter().map(|n| n.estimated_cost).sum();
        let utilized_budget: f64 = all_needs.iter()
            .filter(|n| n.status == TrainingStatus::Completed || n.status == TrainingStatus::InProgress)
            .map(|n| n.estimated_cost)
            .sum();
        
        let avg_completion_rate = if !all_needs.is_empty() {
            all_needs.iter().map(|n| n.completion_percentage).sum::<f64>() / all_needs.len() as f64
        } else {
            0.0
        };
        
        let avg_roi = if !all_needs.is_empty() {
            all_needs.iter().map(|n| n.expected_roi).sum::<f64>() / all_needs.len() as f64
        } else {
            0.0
        };
        
        // Department metrics
        let department_metrics = Self::calculate_department_metrics(&all_needs)?;
        
        // Training type metrics
        let type_metrics = Self::calculate_type_metrics(&all_needs, &all_programs)?;
        
        Ok(TrainingMetrics {
            total_employees,
            employees_in_training,
            completed_this_month,
            completed_this_quarter: completed_this_month * 3, // Simplified
            upcoming_sessions: all_programs.iter()
                .filter(|p| p.start_date.is_some())
                .count() as i32,
            total_budget_allocated: total_budget,
            budget_utilized: utilized_budget,
            budget_remaining: total_budget - utilized_budget,
            average_completion_rate: avg_completion_rate,
            average_satisfaction_score: 8.5, // Placeholder
            average_roi: avg_roi,
            skills_gap_closure_rate: 75.0, // Placeholder
            certification_achievement_rate: 85.0, // Placeholder
            training_hours_delivered: all_paths.iter()
                .map(|p| p.total_duration_hours * (p.progress_percentage / 100.0))
                .sum(),
            cost_per_employee: if total_employees > 0 {
                utilized_budget / total_employees as f64
            } else {
                0.0
            },
            by_department: department_metrics,
            by_training_type: type_metrics,
            period_start,
            period_end,
        })
    }
    
    // Helper methods
    fn validate_training_need(request: &CreateTrainingNeedRequest) -> Result<(), String> {
        if request.employee_name.is_empty() {
            return Err("Employee name is required".to_string());
        }
        if request.department.is_empty() {
            return Err("Department is required".to_string());
        }
        if request.estimated_cost < 0.0 {
            return Err("Estimated cost cannot be negative".to_string());
        }
        Ok(())
    }
    
    fn calculate_priority(gaps: &[SkillGap]) -> Priority {
        let critical_count = gaps.iter()
            .filter(|g| g.importance == Importance::Critical)
            .count();
        
        let high_gap_count = gaps.iter()
            .filter(|g| g.gap_score > 5)
            .count();
        
        if critical_count > 2 || high_gap_count > 5 {
            Priority::Critical
        } else if critical_count > 0 || high_gap_count > 2 {
            Priority::High
        } else if high_gap_count > 0 {
            Priority::Medium
        } else {
            Priority::Low
        }
    }
    
    fn calculate_overall_gap_score(gaps: &[SkillGap]) -> f64 {
        if gaps.is_empty() {
            return 0.0;
        }
        
        let weighted_sum: f64 = gaps.iter().map(|g| {
            let weight = match g.importance {
                Importance::Critical => 3.0,
                Importance::High => 2.0,
                Importance::Medium => 1.5,
                Importance::Low => 1.0,
            };
            g.gap_score as f64 * weight
        }).sum();
        
        let max_possible: f64 = gaps.len() as f64 * 9.0 * 3.0; // Max gap * max weight
        
        (weighted_sum / max_possible) * 100.0
    }
    
    fn determine_priority(score: f64, gaps: &[SkillGap]) -> Priority {
        if score > 70.0 || gaps.iter().any(|g| g.importance == Importance::Critical && g.gap_score > 5) {
            Priority::Critical
        } else if score > 50.0 {
            Priority::High
        } else if score > 30.0 {
            Priority::Medium
        } else {
            Priority::Low
        }
    }
    
    fn calculate_match_score(need: &TrainingNeed, program: &TrainingProgram) -> f64 {
        let mut score = 0.0;
        
        // Type match
        if need.training_type == program.category {
            score += 0.3;
        }
        
        // Cost fitness
        if program.cost_per_person <= need.estimated_cost * 1.2 {
            score += 0.2;
        }
        
        // Effectiveness
        score += program.effectiveness_score / 10.0 * 0.3;
        
        // Completion rate
        score += program.completion_rate / 100.0 * 0.2;
        
        score
    }
    
    fn estimate_training_hours(recommendations: &[TrainingRecommendation]) -> f64 {
        recommendations.iter().map(|r| r.duration_hours).sum()
    }
    
    fn estimate_total_cost(recommendations: &[TrainingRecommendation]) -> f64 {
        recommendations.iter().map(|r| r.cost).sum()
    }
    
    fn calculate_completion_date(total_hours: f64) -> String {
        // Assume 20 hours per week of training
        let weeks = (total_hours / 20.0).ceil() as i64;
        let completion = Utc::now() + chrono::Duration::weeks(weeks);
        completion.format("%Y-%m-%d").to_string()
    }
    
    // Database operations (placeholders - implement with actual DB layer)
    fn save_training_need(need: TrainingNeed) -> Result<TrainingNeed, String> {
        // TODO: Implement actual database save
        Ok(need)
    }
    
    fn fetch_training_needs(_filter: Option<TrainingNeedFilter>) -> Result<Vec<TrainingNeed>, String> {
        // TODO: Implement actual database fetch with filters
        Ok(Vec::new())
    }
    
    fn get_training_need_by_id(_id: i64) -> Result<TrainingNeed, String> {
        // TODO: Implement actual database fetch
        Err("Not implemented".to_string())
    }
    
    fn delete_from_db(_id: i64) -> Result<bool, String> {
        // TODO: Implement actual database delete
        Ok(true)
    }
    
    fn has_active_enrollments(_need_id: i64) -> Result<bool, String> {
        // TODO: Check for active enrollments
        Ok(false)
    }
    
    fn get_employees_by_ids(_ids: Vec<String>) -> Result<Vec<Employee>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn get_employees_by_department(_dept: String) -> Result<Vec<Employee>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn get_all_employees() -> Result<Vec<Employee>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn calculate_skill_gaps(_employee: &Employee) -> Result<Vec<SkillGap>, String> {
        // TODO: Implement skill gap calculation
        Ok(Vec::new())
    }
    
    fn generate_recommendations(_gaps: &[SkillGap]) -> Result<Vec<TrainingRecommendation>, String> {
        // TODO: Implement recommendation engine
        Ok(Vec::new())
    }
    
    fn save_training_program(program: TrainingProgram) -> Result<TrainingProgram, String> {
        // TODO: Implement
        Ok(program)
    }
    
    fn program_exists(_name: &str) -> Result<bool, String> {
        // TODO: Check if program exists
        Ok(false)
    }
    
    fn get_all_programs() -> Result<Vec<TrainingProgram>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn validate_training_program(program: &TrainingProgram) -> Result<(), String> {
        if program.name.is_empty() {
            return Err("Program name is required".to_string());
        }
        if program.cost_per_person < 0.0 {
            return Err("Cost cannot be negative".to_string());
        }
        Ok(())
    }
    
    fn get_employee_skills(_employee_id: &str) -> Result<Vec<Skill>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn get_role_requirements(_role: &str) -> Result<Vec<Skill>, String> {
        // TODO: Implement
        Ok(Vec::new())
    }
    
    fn identify_gaps(_current: &[Skill], _target: &[Skill]) -> Vec<SkillGap> {
        // TODO: Implement gap identification
        Vec::new()
    }
    
    fn build_learning_modules(_gaps: &[SkillGap]) -> Result<Vec<LearningModule>, String> {
        // TODO: Build learning modules based on gaps
        Ok(Vec::new())
    }
    
    fn calculate_path_cost(_modules: &[LearningModule]) -> Result<f64, String> {
        // TODO: Calculate total cost for learning path
        Ok(0.0)
    }
    
    fn get_employee_name(_employee_id: &str) -> Result<String, String> {
        // TODO: Fetch employee name
        Ok("Employee Name".to_string())
    }
    
    fn save_learning_path(path: LearningPath) -> Result<LearningPath, String> {
        // TODO: Save to database
        Ok(path)
    }
    
    fn get_learning_path_by_id(_id: i64) -> Result<LearningPath, String> {
        // TODO: Fetch from database
        Err("Not implemented".to_string())
    }
    
    fn get_all_learning_paths() -> Result<Vec<LearningPath>, String> {
        // TODO: Fetch all paths
        Ok(Vec::new())
    }
    
    fn get_total_employee_count() -> Result<i32, String> {
        // TODO: Get total employee count
        Ok(100)
    }
    
    fn calculate_department_metrics(needs: &[TrainingNeed]) -> Result<Vec<DepartmentMetrics>, String> {
        let mut dept_map: HashMap<String, DepartmentMetrics> = HashMap::new();
        
        for need in needs {
            let entry = dept_map.entry(need.department.clone()).or_insert(DepartmentMetrics {
                department: need.department.clone(),
                employee_count: 0,
                training_count: 0,
                completion_rate: 0.0,
                budget_allocated: 0.0,
                budget_spent: 0.0,
                average_satisfaction: 0.0,
            });
            
            entry.training_count += 1;
            entry.budget_allocated += need.estimated_cost;
            if need.status == TrainingStatus::Completed || need.status == TrainingStatus::InProgress {
                entry.budget_spent += need.estimated_cost;
            }
        }
        
        Ok(dept_map.into_values().collect())
    }
    
    fn calculate_type_metrics(
        needs: &[TrainingNeed],
        _programs: &[TrainingProgram],
    ) -> Result<Vec<TrainingTypeMetrics>, String> {
        let mut type_map: HashMap<TrainingType, TrainingTypeMetrics> = HashMap::new();
        
        for need in needs {
            let entry = type_map.entry(need.training_type.clone()).or_insert(TrainingTypeMetrics {
                training_type: need.training_type.clone(),
                count: 0,
                total_participants: 0,
                completion_rate: 0.0,
                average_cost: 0.0,
                average_satisfaction: 0.0,
            });
            
            entry.count += 1;
            entry.total_participants += 1;
            entry.average_cost = (entry.average_cost * (entry.count - 1) as f64 + need.estimated_cost) / entry.count as f64;
        }
        
        Ok(type_map.into_values().collect())
    }
}

// Placeholder structs for Employee and Skill - will be used for future employee management features
#[allow(dead_code)]
#[derive(Debug, Clone)]
struct Employee {
    id: String,
    name: String,
    department: String,
    position: String,
}

impl Employee {
    #[allow(dead_code)]
    pub fn new(id: String, name: String, department: String, position: String) -> Self {
        Self { id, name, department, position }
    }
    
    #[allow(dead_code)]
    pub fn get_department(&self) -> &str {
        &self.department
    }
    
    #[allow(dead_code)]
    pub fn get_position(&self) -> &str {
        &self.position
    }
}

#[allow(dead_code)]
#[derive(Debug, Clone)]
struct Skill {
    name: String,
    level: i32,
}

impl Skill {
    #[allow(dead_code)]
    pub fn new(name: String, level: i32) -> Self {
        Self { name, level }
    }
    
    #[allow(dead_code)]
    pub fn get_name(&self) -> &str {
        &self.name
    }
    
    #[allow(dead_code)]
    pub fn get_level(&self) -> i32 {
        self.level
    }
}