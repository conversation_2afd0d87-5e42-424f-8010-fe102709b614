use super::models::*;
use chrono::Utc;

#[allow(dead_code)]
pub struct ApprovalEngine;

#[allow(dead_code)]
impl ApprovalEngine {
    pub fn create_workflow(
        request_type: String,
        total_budget: f64,
        submitted_by: String,
    ) -> ApprovalWorkflow {
        let stages = Self::determine_approval_stages(&request_type, total_budget);
        
        ApprovalWorkflow {
            id: None,
            request_id: Self::generate_request_id(),
            request_type,
            training_need_id: None,
            total_budget,
            currency: "USD".to_string(),
            status: ApprovalStatus::Pending,
            current_stage: 1,
            stages,
            submitted_by,
            submitted_at: Utc::now(),
            completed_at: None,
            notes: None,
        }
    }
    
    pub fn process_decision(
        workflow: &mut ApprovalWorkflow,
        stage_number: i32,
        decision: ApprovalDecision,
        _approver_name: String,
        comments: Option<String>,
    ) -> Result<(), String> {
        if workflow.current_stage != stage_number {
            return Err(format!("Cannot process stage {}. Current stage is {}", 
                stage_number, workflow.current_stage));
        }
        
        if let Some(stage) = workflow.stages.iter_mut()
            .find(|s| s.stage_number == stage_number) {
            
            stage.decision = Some(decision.clone());
            stage.comments = comments;
            stage.decided_at = Some(Utc::now());
            
            match decision {
                ApprovalDecision::Approve => {
                    if Self::is_final_stage(workflow, stage_number) {
                        workflow.status = ApprovalStatus::Approved;
                        workflow.completed_at = Some(Utc::now());
                    } else {
                        workflow.current_stage += 1;
                        workflow.status = ApprovalStatus::InProgress;
                    }
                }
                ApprovalDecision::Reject => {
                    workflow.status = ApprovalStatus::Rejected;
                    workflow.completed_at = Some(Utc::now());
                }
                ApprovalDecision::Revise => {
                    workflow.status = ApprovalStatus::Revision;
                }
                ApprovalDecision::Escalate => {
                    Self::escalate_to_next_level(workflow)?;
                }
            }
            
            Ok(())
        } else {
            Err(format!("Stage {} not found in workflow", stage_number))
        }
    }
    
    pub fn check_escalation_needed(workflow: &ApprovalWorkflow) -> bool {
        if let Some(current_stage) = workflow.stages
            .iter()
            .find(|s| s.stage_number == workflow.current_stage) {
            
            if let Some(escalation_days) = current_stage.escalation_days {
                if let Some(submitted_at) = current_stage.decided_at {
                    let days_elapsed = (Utc::now() - submitted_at).num_days();
                    return days_elapsed > escalation_days as i64;
                }
            }
        }
        false
    }
    
    fn determine_approval_stages(_request_type: &str, budget: f64) -> Vec<ApprovalStage> {
        let mut stages = Vec::new();
        let mut stage_number = 1;
        
        stages.push(ApprovalStage {
            stage_number,
            approver_name: "Direct Manager".to_string(),
            approver_role: ApproverRole::Manager,
            approver_email: None,
            department: None,
            decision: None,
            comments: None,
            decided_at: None,
            is_required: true,
            escalation_days: Some(3),
        });
        stage_number += 1;
        
        if budget > 5000.0 {
            stages.push(ApprovalStage {
                stage_number,
                approver_name: "Department Head".to_string(),
                approver_role: ApproverRole::DepartmentHead,
                approver_email: None,
                department: None,
                decision: None,
                comments: None,
                decided_at: None,
                is_required: true,
                escalation_days: Some(5),
            });
            stage_number += 1;
        }
        
        stages.push(ApprovalStage {
            stage_number,
            approver_name: "HR Manager".to_string(),
            approver_role: ApproverRole::HR,
            approver_email: None,
            department: Some("Human Resources".to_string()),
            decision: None,
            comments: None,
            decided_at: None,
            is_required: true,
            escalation_days: Some(3),
        });
        stage_number += 1;
        
        if budget > 10000.0 {
            stages.push(ApprovalStage {
                stage_number,
                approver_name: "Finance Manager".to_string(),
                approver_role: ApproverRole::Finance,
                approver_email: None,
                department: Some("Finance".to_string()),
                decision: None,
                comments: None,
                decided_at: None,
                is_required: true,
                escalation_days: Some(5),
            });
            stage_number += 1;
        }
        
        if budget > 25000.0 {
            stages.push(ApprovalStage {
                stage_number,
                approver_name: "Executive".to_string(),
                approver_role: ApproverRole::Executive,
                approver_email: None,
                department: None,
                decision: None,
                comments: None,
                decided_at: None,
                is_required: true,
                escalation_days: Some(7),
            });
        }
        
        stages
    }
    
    fn is_final_stage(workflow: &ApprovalWorkflow, stage_number: i32) -> bool {
        workflow.stages
            .iter()
            .filter(|s| s.is_required)
            .last()
            .map(|s| s.stage_number == stage_number)
            .unwrap_or(false)
    }
    
    fn escalate_to_next_level(workflow: &mut ApprovalWorkflow) -> Result<(), String> {
        let next_escalation_stage = workflow.stages
            .iter()
            .find(|s| {
                s.stage_number > workflow.current_stage && 
                matches!(s.approver_role, ApproverRole::DepartmentHead | ApproverRole::Executive)
            });
        
        if let Some(escalation_stage) = next_escalation_stage {
            workflow.current_stage = escalation_stage.stage_number;
            workflow.status = ApprovalStatus::InProgress;
            Ok(())
        } else {
            Err("No escalation path available".to_string())
        }
    }
    
    fn generate_request_id() -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let timestamp = Utc::now().timestamp();
        let random: u16 = rng.gen();
        format!("REQ-{}-{:04}", timestamp, random)
    }
    
    pub fn get_pending_approvals(
        workflows: &[ApprovalWorkflow],
        approver_role: Option<ApproverRole>,
        approver_name: Option<String>,
    ) -> Vec<ApprovalWorkflow> {
        workflows
            .iter()
            .filter(|w| {
                matches!(w.status, ApprovalStatus::Pending | ApprovalStatus::InProgress) &&
                w.stages
                    .iter()
                    .any(|s| {
                        s.stage_number == w.current_stage &&
                        s.decision.is_none() &&
                        (approver_role.is_none() || Some(&s.approver_role) == approver_role.as_ref()) &&
                        (approver_name.is_none() || s.approver_name.contains(approver_name.as_ref().unwrap()))
                    })
            })
            .cloned()
            .collect()
    }
    
    pub fn calculate_approval_metrics(workflows: &[ApprovalWorkflow]) -> ApprovalMetrics {
        let total = workflows.len();
        let approved = workflows.iter().filter(|w| w.status == ApprovalStatus::Approved).count();
        let rejected = workflows.iter().filter(|w| w.status == ApprovalStatus::Rejected).count();
        let pending = workflows.iter().filter(|w| matches!(w.status, ApprovalStatus::Pending | ApprovalStatus::InProgress)).count();
        
        let avg_approval_time = Self::calculate_average_approval_time(workflows);
        let approval_rate = if total > 0 {
            (approved as f64 / total as f64) * 100.0
        } else {
            0.0
        };
        
        ApprovalMetrics {
            total_requests: total,
            approved,
            rejected,
            pending,
            average_approval_days: avg_approval_time,
            approval_rate,
            escalation_count: workflows.iter()
                .filter(|w| {
                    w.stages.iter().any(|s| matches!(s.decision, Some(ApprovalDecision::Escalate)))
                })
                .count(),
        }
    }
    
    fn calculate_average_approval_time(workflows: &[ApprovalWorkflow]) -> f64 {
        let completed_workflows: Vec<_> = workflows
            .iter()
            .filter(|w| w.completed_at.is_some())
            .collect();
        
        if completed_workflows.is_empty() {
            return 0.0;
        }
        
        let total_days: i64 = completed_workflows
            .iter()
            .map(|w| {
                let completed = w.completed_at.unwrap();
                (completed - w.submitted_at).num_days()
            })
            .sum();
        
        total_days as f64 / completed_workflows.len() as f64
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ApprovalMetrics {
    pub total_requests: usize,
    pub approved: usize,
    pub rejected: usize,
    pub pending: usize,
    pub average_approval_days: f64,
    pub approval_rate: f64,
    pub escalation_count: usize,
}