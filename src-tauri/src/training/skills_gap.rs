use super::models::*;

#[allow(dead_code)]
pub struct SkillsGapAnalyzer;

#[allow(dead_code)]
impl SkillsGapAnalyzer {
    pub fn analyze_individual_gaps(
        employee_skills: &[SkillAssessment],
        required_skills: &[SkillRequirement],
    ) -> Vec<SkillGap> {
        let mut gaps = Vec::new();
        
        for requirement in required_skills {
            let current_skill = employee_skills
                .iter()
                .find(|s| s.skill_name == requirement.skill_name);
            
            let current_level = current_skill.map(|s| s.proficiency_level).unwrap_or(0);
            let gap_score = requirement.required_level - current_level;
            
            if gap_score > 0 {
                gaps.push(SkillGap {
                    skill_name: requirement.skill_name.clone(),
                    current_level,
                    required_level: requirement.required_level,
                    gap_score,
                    importance: requirement.importance.clone(),
                    trend: current_skill.and_then(|s| s.trend.clone()),
                });
            }
        }
        
        gaps.sort_by(|a, b| {
            let importance_order = match (&b.importance, &a.importance) {
                (Importance::Critical, _) => std::cmp::Ordering::Less,
                (_, Importance::Critical) => std::cmp::Ordering::Greater,
                (Importance::High, Importance::High) => std::cmp::Ordering::Equal,
                (Importance::High, _) => std::cmp::Ordering::Less,
                (_, Importance::High) => std::cmp::Ordering::Greater,
                (Importance::Medium, Importance::Medium) => std::cmp::Ordering::Equal,
                (Importance::Medium, _) => std::cmp::Ordering::Less,
                (_, Importance::Medium) => std::cmp::Ordering::Greater,
                _ => std::cmp::Ordering::Equal,
            };
            
            if importance_order == std::cmp::Ordering::Equal {
                b.gap_score.cmp(&a.gap_score)
            } else {
                importance_order
            }
        });
        
        gaps
    }
    
    pub fn calculate_team_gaps(
        team_assessments: &[TeamSkillAssessment],
    ) -> TeamGapAnalysis {
        let mut all_gaps = Vec::new();
        let mut critical_gaps = Vec::new();
        let mut strengths = Vec::new();
        
        for assessment in team_assessments {
            for gap in &assessment.individual_gaps {
                all_gaps.push(gap.clone());
                
                if gap.importance == Importance::Critical && gap.gap_score > 3 {
                    critical_gaps.push(gap.clone());
                }
                
                if gap.gap_score <= 0 {
                    strengths.push(gap.clone());
                }
            }
        }
        
        let average_gap_score = if !all_gaps.is_empty() {
            all_gaps.iter().map(|g| g.gap_score as f64).sum::<f64>() / all_gaps.len() as f64
        } else {
            0.0
        };
        
        TeamGapAnalysis {
            team_id: "default".to_string(),
            team_name: "Team".to_string(),
            total_members: team_assessments.len() as i32,
            average_gap_score,
            critical_gaps: Self::deduplicate_gaps(critical_gaps),
            common_gaps: Self::find_common_gaps(&all_gaps, team_assessments.len()),
            team_strengths: Self::deduplicate_gaps(strengths),
            training_priorities: Self::determine_priorities(&all_gaps),
        }
    }
    
    fn deduplicate_gaps(gaps: Vec<SkillGap>) -> Vec<SkillGap> {
        let mut unique_gaps = Vec::new();
        let mut seen_skills = std::collections::HashSet::new();
        
        for gap in gaps {
            if seen_skills.insert(gap.skill_name.clone()) {
                unique_gaps.push(gap);
            }
        }
        
        unique_gaps
    }
    
    fn find_common_gaps(all_gaps: &[SkillGap], team_size: usize) -> Vec<SkillGap> {
        use std::collections::HashMap;
        
        let mut gap_counts: HashMap<String, (SkillGap, usize)> = HashMap::new();
        
        for gap in all_gaps {
            gap_counts
                .entry(gap.skill_name.clone())
                .and_modify(|e| e.1 += 1)
                .or_insert((gap.clone(), 1));
        }
        
        let threshold = (team_size as f64 * 0.5).ceil() as usize;
        
        gap_counts
            .into_values()
            .filter(|(_, count)| *count >= threshold)
            .map(|(gap, _)| gap)
            .collect()
    }
    
    fn determine_priorities(gaps: &[SkillGap]) -> Vec<TrainingPriority> {
        use std::collections::HashMap;
        
        let mut priority_groups: HashMap<Importance, Vec<SkillGap>> = HashMap::new();
        
        for gap in gaps {
            priority_groups
                .entry(gap.importance.clone())
                .or_insert_with(Vec::new)
                .push(gap.clone());
        }
        
        let mut priorities = Vec::new();
        
        for (importance, gaps) in priority_groups {
            let priority_level = match importance {
                Importance::Critical => Priority::Critical,
                Importance::High => Priority::High,
                Importance::Medium => Priority::Medium,
                Importance::Low => Priority::Low,
            };
            
            priorities.push(TrainingPriority {
                priority_level,
                skills: gaps.iter().map(|g| g.skill_name.clone()).collect(),
                estimated_hours: gaps.len() as f64 * 20.0,
                recommended_timeline: Self::calculate_timeline(&importance),
            });
        }
        
        priorities.sort_by(|a, b| {
            match (&b.priority_level, &a.priority_level) {
                (Priority::Critical, _) => std::cmp::Ordering::Less,
                (_, Priority::Critical) => std::cmp::Ordering::Greater,
                (Priority::High, Priority::High) => std::cmp::Ordering::Equal,
                (Priority::High, _) => std::cmp::Ordering::Less,
                (_, Priority::High) => std::cmp::Ordering::Greater,
                (Priority::Medium, Priority::Medium) => std::cmp::Ordering::Equal,
                (Priority::Medium, _) => std::cmp::Ordering::Less,
                (_, Priority::Medium) => std::cmp::Ordering::Greater,
                _ => std::cmp::Ordering::Equal,
            }
        });
        
        priorities
    }
    
    fn calculate_timeline(importance: &Importance) -> String {
        match importance {
            Importance::Critical => "Within 1 month".to_string(),
            Importance::High => "Within 3 months".to_string(),
            Importance::Medium => "Within 6 months".to_string(),
            Importance::Low => "Within 12 months".to_string(),
        }
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SkillAssessment {
    pub skill_name: String,
    pub proficiency_level: i32,
    pub last_assessed: String,
    pub trend: Option<Trend>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SkillRequirement {
    pub skill_name: String,
    pub required_level: i32,
    pub importance: Importance,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TeamSkillAssessment {
    pub employee_id: String,
    pub employee_name: String,
    pub individual_gaps: Vec<SkillGap>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TeamGapAnalysis {
    pub team_id: String,
    pub team_name: String,
    pub total_members: i32,
    pub average_gap_score: f64,
    pub critical_gaps: Vec<SkillGap>,
    pub common_gaps: Vec<SkillGap>,
    pub team_strengths: Vec<SkillGap>,
    pub training_priorities: Vec<TrainingPriority>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TrainingPriority {
    pub priority_level: Priority,
    pub skills: Vec<String>,
    pub estimated_hours: f64,
    pub recommended_timeline: String,
}