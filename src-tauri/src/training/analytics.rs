use super::models::*;
use std::collections::HashMap;

#[allow(dead_code)]
pub struct TrainingAnalytics;

#[allow(dead_code)]
impl TrainingAnalytics {
    pub fn calculate_roi(
        investment: f64,
        productivity_gain: f64,
        retention_improvement: f64,
        error_reduction: f64,
    ) -> RoiCalculation {
        let total_benefits = productivity_gain + retention_improvement + error_reduction;
        let net_benefit = total_benefits - investment;
        let roi_percentage = if investment > 0.0 {
            (net_benefit / investment) * 100.0
        } else {
            0.0
        };
        
        let payback_period_months = if total_benefits > 0.0 {
            (investment / (total_benefits / 12.0)).ceil() as i32
        } else {
            0
        };
        
        RoiCalculation {
            initial_investment: investment,
            total_benefits,
            net_benefit,
            roi_percentage,
            payback_period_months,
            benefit_breakdown: BenefitBreakdown {
                productivity_gain,
                retention_improvement,
                error_reduction,
                customer_satisfaction: 0.0,
                compliance_benefits: 0.0,
            },
        }
    }
    
    pub fn analyze_completion_trends(
        training_records: &[TrainingRecord],
        period_months: i32,
    ) -> CompletionTrends {
        let mut monthly_completions = HashMap::new();
        let mut department_completions = HashMap::new();
        let mut training_type_completions = HashMap::new();
        
        for record in training_records {
            let month_key = record.completion_date.format("%Y-%m").to_string();
            *monthly_completions.entry(month_key).or_insert(0) += 1;
            
            *department_completions.entry(record.department.clone()).or_insert(0) += 1;
            
            *training_type_completions.entry(record.training_type.clone()).or_insert(0) += 1;
        }
        
        let total_completions = training_records.len();
        let average_per_month = if period_months > 0 {
            total_completions as f64 / period_months as f64
        } else {
            0.0
        };
        
        let trend = Self::calculate_trend(&monthly_completions);
        
        CompletionTrends {
            total_completions,
            average_per_month,
            trend,
            by_month: monthly_completions,
            by_department: department_completions,
            by_training_type: training_type_completions,
        }
    }
    
    pub fn generate_performance_report(
        pre_training_scores: &[AssessmentScore],
        post_training_scores: &[AssessmentScore],
    ) -> PerformanceReport {
        let mut improvements = Vec::new();
        let mut total_improvement = 0.0;
        let mut count = 0;
        
        for pre_score in pre_training_scores {
            if let Some(post_score) = post_training_scores
                .iter()
                .find(|s| s.employee_id == pre_score.employee_id && s.skill_name == pre_score.skill_name) {
                
                let improvement = post_score.score - pre_score.score;
                improvements.push(SkillImprovement {
                    employee_id: pre_score.employee_id.clone(),
                    skill_name: pre_score.skill_name.clone(),
                    pre_training_score: pre_score.score,
                    post_training_score: post_score.score,
                    improvement_percentage: (improvement / pre_score.score) * 100.0,
                });
                
                total_improvement += improvement;
                count += 1;
            }
        }
        
        let average_improvement = if count > 0 {
            total_improvement / count as f64
        } else {
            0.0
        };
        
        let success_rate = improvements
            .iter()
            .filter(|i| i.improvement_percentage > 0.0)
            .count() as f64 / improvements.len().max(1) as f64 * 100.0;
        
        PerformanceReport {
            total_assessed: count,
            average_improvement,
            success_rate,
            skill_improvements: improvements,
            recommendations: Self::generate_recommendations(average_improvement, success_rate),
        }
    }
    
    pub fn budget_utilization_analysis(
        allocated_budget: f64,
        spent_budget: f64,
        planned_trainings: &[PlannedTraining],
    ) -> BudgetAnalysis {
        let utilization_rate = if allocated_budget > 0.0 {
            (spent_budget / allocated_budget) * 100.0
        } else {
            0.0
        };
        
        let remaining_budget = allocated_budget - spent_budget;
        
        let mut by_department = HashMap::new();
        let mut by_category = HashMap::new();
        
        for training in planned_trainings {
            *by_department.entry(training.department.clone()).or_insert(0.0) += training.cost;
            *by_category.entry(format!("{:?}", training.training_type)).or_insert(0.0) += training.cost;
        }
        
        let cost_per_employee = if !planned_trainings.is_empty() {
            spent_budget / planned_trainings.len() as f64
        } else {
            0.0
        };
        
        let projected_overage = planned_trainings
            .iter()
            .map(|t| t.cost)
            .sum::<f64>() - allocated_budget;
        
        BudgetAnalysis {
            allocated_budget,
            spent_budget,
            remaining_budget,
            utilization_rate,
            projected_overage: projected_overage.max(0.0),
            cost_per_employee,
            by_department,
            by_category,
            recommendations: Self::generate_budget_recommendations(utilization_rate, projected_overage),
        }
    }
    
    pub fn effectiveness_scoring(
        program: &TrainingProgram,
        feedback: &[ParticipantFeedback],
    ) -> EffectivenessScore {
        let avg_satisfaction = if !feedback.is_empty() {
            feedback.iter().map(|f| f.satisfaction_score).sum::<f64>() / feedback.len() as f64
        } else {
            0.0
        };
        
        let knowledge_retention = Self::calculate_knowledge_retention(feedback);
        let skill_application = Self::calculate_skill_application(feedback);
        let business_impact = Self::calculate_business_impact(program, feedback);
        
        let overall_score = (avg_satisfaction * 0.2 + 
                           knowledge_retention * 0.3 + 
                           skill_application * 0.3 + 
                           business_impact * 0.2) / 10.0 * 100.0;
        
        EffectivenessScore {
            program_id: program.id.unwrap_or(0),
            program_name: program.name.clone(),
            overall_score,
            satisfaction_score: avg_satisfaction,
            knowledge_retention_score: knowledge_retention,
            skill_application_score: skill_application,
            business_impact_score: business_impact,
            participant_count: feedback.len(),
            recommendations: Self::generate_effectiveness_recommendations(overall_score),
        }
    }
    
    fn calculate_trend(monthly_data: &HashMap<String, i32>) -> String {
        if monthly_data.len() < 2 {
            return "Insufficient data".to_string();
        }
        
        let mut sorted_months: Vec<_> = monthly_data.iter().collect();
        sorted_months.sort_by_key(|&(month, _)| month);
        
        let recent_avg = sorted_months.iter()
            .rev()
            .take(3)
            .map(|(_, &count)| count as f64)
            .sum::<f64>() / 3.0;
        
        let older_avg = sorted_months.iter()
            .rev()
            .skip(3)
            .take(3)
            .map(|(_, &count)| count as f64)
            .sum::<f64>() / 3.0;
        
        if recent_avg > older_avg * 1.1 {
            "Increasing".to_string()
        } else if recent_avg < older_avg * 0.9 {
            "Decreasing".to_string()
        } else {
            "Stable".to_string()
        }
    }
    
    fn calculate_knowledge_retention(feedback: &[ParticipantFeedback]) -> f64 {
        if feedback.is_empty() {
            return 0.0;
        }
        
        feedback.iter()
            .filter_map(|f| f.knowledge_retention_score)
            .sum::<f64>() / feedback.len() as f64
    }
    
    fn calculate_skill_application(feedback: &[ParticipantFeedback]) -> f64 {
        if feedback.is_empty() {
            return 0.0;
        }
        
        feedback.iter()
            .filter_map(|f| f.skill_application_score)
            .sum::<f64>() / feedback.len() as f64
    }
    
    fn calculate_business_impact(program: &TrainingProgram, feedback: &[ParticipantFeedback]) -> f64 {
        let base_impact = program.effectiveness_score;
        let feedback_modifier = if !feedback.is_empty() {
            feedback.iter()
                .filter_map(|f| f.business_impact_score)
                .sum::<f64>() / feedback.len() as f64
        } else {
            1.0
        };
        
        (base_impact * feedback_modifier).min(10.0)
    }
    
    fn generate_recommendations(avg_improvement: f64, success_rate: f64) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if avg_improvement < 5.0 {
            recommendations.push("Consider revising training content or delivery methods".to_string());
        }
        
        if success_rate < 70.0 {
            recommendations.push("Investigate reasons for low success rate and provide additional support".to_string());
        }
        
        if avg_improvement > 15.0 && success_rate > 85.0 {
            recommendations.push("Excellent results! Consider expanding this training program".to_string());
        }
        
        recommendations
    }
    
    fn generate_budget_recommendations(utilization_rate: f64, projected_overage: f64) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if utilization_rate < 50.0 {
            recommendations.push("Budget underutilized. Consider accelerating training initiatives".to_string());
        }
        
        if utilization_rate > 90.0 {
            recommendations.push("Budget nearly exhausted. Plan for next period allocation".to_string());
        }
        
        if projected_overage > 0.0 {
            recommendations.push(format!("Projected overage of ${:.2}. Consider prioritizing critical trainings", projected_overage));
        }
        
        recommendations
    }
    
    fn generate_effectiveness_recommendations(overall_score: f64) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if overall_score < 60.0 {
            recommendations.push("Program needs significant improvement. Review content and delivery".to_string());
        } else if overall_score < 75.0 {
            recommendations.push("Program performing adequately. Focus on identified weak areas".to_string());
        } else {
            recommendations.push("High-performing program. Consider as a model for others".to_string());
        }
        
        recommendations
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct RoiCalculation {
    pub initial_investment: f64,
    pub total_benefits: f64,
    pub net_benefit: f64,
    pub roi_percentage: f64,
    pub payback_period_months: i32,
    pub benefit_breakdown: BenefitBreakdown,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct BenefitBreakdown {
    pub productivity_gain: f64,
    pub retention_improvement: f64,
    pub error_reduction: f64,
    pub customer_satisfaction: f64,
    pub compliance_benefits: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CompletionTrends {
    pub total_completions: usize,
    pub average_per_month: f64,
    pub trend: String,
    pub by_month: HashMap<String, i32>,
    pub by_department: HashMap<String, i32>,
    pub by_training_type: HashMap<TrainingType, i32>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PerformanceReport {
    pub total_assessed: usize,
    pub average_improvement: f64,
    pub success_rate: f64,
    pub skill_improvements: Vec<SkillImprovement>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SkillImprovement {
    pub employee_id: String,
    pub skill_name: String,
    pub pre_training_score: f64,
    pub post_training_score: f64,
    pub improvement_percentage: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct BudgetAnalysis {
    pub allocated_budget: f64,
    pub spent_budget: f64,
    pub remaining_budget: f64,
    pub utilization_rate: f64,
    pub projected_overage: f64,
    pub cost_per_employee: f64,
    pub by_department: HashMap<String, f64>,
    pub by_category: HashMap<String, f64>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct EffectivenessScore {
    pub program_id: i64,
    pub program_name: String,
    pub overall_score: f64,
    pub satisfaction_score: f64,
    pub knowledge_retention_score: f64,
    pub skill_application_score: f64,
    pub business_impact_score: f64,
    pub participant_count: usize,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TrainingRecord {
    pub employee_id: String,
    pub department: String,
    pub training_type: TrainingType,
    pub completion_date: chrono::DateTime<chrono::Utc>,
    pub score: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AssessmentScore {
    pub employee_id: String,
    pub skill_name: String,
    pub score: f64,
    pub assessment_date: String,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PlannedTraining {
    pub training_id: String,
    pub department: String,
    pub training_type: TrainingType,
    pub cost: f64,
    pub scheduled_date: String,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ParticipantFeedback {
    pub participant_id: String,
    pub program_id: i64,
    pub satisfaction_score: f64,
    pub knowledge_retention_score: Option<f64>,
    pub skill_application_score: Option<f64>,
    pub business_impact_score: Option<f64>,
    pub comments: Option<String>,
}