use serde::{Deserialize, Serialize};
use tauri::State;
use std::sync::Arc;
use tokio::sync::Mutex;

// Since we're in src/commands/, we need to go up one level to access training module
use crate::training;
use training::models::*;
use training::service::TrainingService;
use training::csv_handler::CsvImporter;

// State management for training data
pub struct TrainingState {
    pub service: Arc<Mutex<TrainingService>>,
}

impl TrainingState {
    pub fn new() -> Self {
        Self {
            service: Arc::new(Mutex::new(TrainingService)),
        }
    }
}

// ============= Training Needs Commands =============

#[tauri::command]
pub async fn create_training_need_enhanced(
    state: State<'_, TrainingState>,
    request: CreateTrainingNeedRequest,
) -> Result<TrainingNeed, String> {
    let service = state.service.lock().await;
    service.create_training_need(request)
}

#[tauri::command]
pub async fn get_training_needs_enhanced(
    state: State<'_, TrainingState>,
    filter: Option<TrainingNeedFilter>,
) -> Result<Vec<TrainingNeed>, String> {
    let service = state.service.lock().await;
    service.get_training_needs(filter)
}

#[tauri::command]
pub async fn update_training_need_enhanced(
    state: State<'_, TrainingState>,
    id: i64,
    updates: TrainingNeed,
) -> Result<TrainingNeed, String> {
    let service = state.service.lock().await;
    service.update_training_need(id, updates)
}

#[tauri::command]
pub async fn delete_training_need_enhanced(
    state: State<'_, TrainingState>,
    id: i64,
) -> Result<bool, String> {
    let service = state.service.lock().await;
    service.delete_training_need(id)
}

// ============= Skills Gap Analysis Commands =============

#[tauri::command]
pub async fn analyze_skills_gap(
    state: State<'_, TrainingState>,
    request: SkillsGapAnalysisRequest,
) -> Result<Vec<SkillsGapAnalysisResult>, String> {
    let service = state.service.lock().await;
    service.analyze_skills_gap(request)
}

#[tauri::command]
pub async fn get_skill_recommendations(
    state: State<'_, TrainingState>,
    employee_id: String,
) -> Result<Vec<TrainingRecommendation>, String> {
    let service = state.service.lock().await;
    
    // Get employee's skill gaps
    let analysis_request = SkillsGapAnalysisRequest {
        employee_ids: Some(vec![employee_id.clone()]),
        department: None,
        include_recommendations: true,
    };
    
    let results = service.analyze_skills_gap(analysis_request)?;
    
    if let Some(result) = results.first() {
        Ok(result.recommendations.clone())
    } else {
        Ok(Vec::new())
    }
}

// ============= Training Programs Commands =============

#[tauri::command]
pub async fn create_training_program_enhanced(
    state: State<'_, TrainingState>,
    program: TrainingProgram,
) -> Result<TrainingProgram, String> {
    let service = state.service.lock().await;
    service.create_training_program(program)
}

#[tauri::command]
pub async fn get_training_programs_enhanced(
    _filter: Option<TrainingProgramFilter>,
) -> Result<Vec<TrainingProgram>, String> {
    // TODO: Implement with actual database fetch
    Ok(Vec::new())
}

#[tauri::command]
pub async fn match_programs_to_needs(
    state: State<'_, TrainingState>,
    need_id: i64,
) -> Result<Vec<TrainingProgram>, String> {
    let service = state.service.lock().await;
    service.match_programs_to_needs(need_id)
}

// ============= Learning Paths Commands =============

#[tauri::command]
pub async fn create_learning_path(
    state: State<'_, TrainingState>,
    employee_id: String,
    target_role: String,
) -> Result<LearningPath, String> {
    let service = state.service.lock().await;
    service.create_learning_path(employee_id, target_role)
}

#[tauri::command]
pub async fn update_module_progress(
    state: State<'_, TrainingState>,
    path_id: i64,
    module_id: String,
    progress: f64,
) -> Result<LearningPath, String> {
    let service = state.service.lock().await;
    service.update_module_progress(path_id, module_id, progress)
}

#[tauri::command]
pub async fn get_learning_paths(
    _employee_id: Option<String>,
) -> Result<Vec<LearningPath>, String> {
    // TODO: Implement with actual database fetch
    Ok(Vec::new())
}

// ============= Approval Workflow Commands =============

#[tauri::command]
pub async fn create_approval_workflow(
    workflow: ApprovalWorkflow,
) -> Result<ApprovalWorkflow, String> {
    // TODO: Implement approval workflow creation
    Ok(workflow)
}

#[tauri::command]
pub async fn process_approval(
    _workflow_id: i64,
    _stage_number: i32,
    _decision: ApprovalDecision,
    _comments: Option<String>,
) -> Result<ApprovalWorkflow, String> {
    // TODO: Implement approval processing
    Err("Not implemented".to_string())
}

#[tauri::command]
pub async fn get_pending_approvals(
    _approver_name: Option<String>,
    _approver_role: Option<ApproverRole>,
) -> Result<Vec<ApprovalWorkflow>, String> {
    // TODO: Implement fetching pending approvals
    Ok(Vec::new())
}

// ============= Analytics Commands =============

#[tauri::command]
pub async fn get_training_metrics(
    state: State<'_, TrainingState>,
    period_start: String,
    period_end: String,
) -> Result<TrainingMetrics, String> {
    let service = state.service.lock().await;
    service.get_training_metrics(period_start, period_end)
}

#[tauri::command]
pub async fn get_department_metrics(
    state: State<'_, TrainingState>,
    department: String,
) -> Result<DepartmentMetrics, String> {
    let service = state.service.lock().await;
    
    // Get all training needs for this department
    let filter = TrainingNeedFilter {
        department: Some(department.clone()),
        status: None,
        priority: None,
        training_type: None,
        date_from: None,
        date_to: None,
        min_cost: None,
        max_cost: None,
    };
    
    let training_needs = service.get_training_needs(Some(filter))?;
    
    // Calculate department metrics
    let total_needs = training_needs.len() as i32;
    let completed_needs = training_needs.iter()
        .filter(|need| matches!(need.status, TrainingStatus::Completed))
        .count() as i32;
    let _in_progress_needs = training_needs.iter()
        .filter(|need| matches!(need.status, TrainingStatus::InProgress))
        .count() as i32;
    let _pending_needs = training_needs.iter()
        .filter(|need| matches!(need.status, TrainingStatus::NotStarted))
        .count() as i32;
    
    // Calculate budget metrics
    let total_budget = training_needs.iter()
        .map(|need| need.estimated_cost)
        .sum::<f64>();
    let spent_budget = training_needs.iter()
        .filter(|need| matches!(need.status, TrainingStatus::Completed))
        .map(|need| need.estimated_cost)
        .sum::<f64>();
    
    // Calculate completion rate
    let completion_rate = if total_needs > 0 {
        (completed_needs as f64 / total_needs as f64) * 100.0
    } else {
        0.0
    };
    
    Ok(DepartmentMetrics {
        department,
        employee_count: 0, // TODO: Get actual employee count
        training_count: total_needs,
        completion_rate,
        budget_allocated: total_budget,
        budget_spent: spent_budget,
        average_satisfaction: 0.0, // TODO: Calculate from feedback
    })
}

#[tauri::command]
pub async fn get_roi_analysis(
    state: State<'_, TrainingState>,
    start_date: String,
    end_date: String,
) -> Result<RoiAnalysis, String> {
    let service = state.service.lock().await;
    
    // Get training needs in the date range
    let filter = TrainingNeedFilter {
        department: None,
        status: Some(TrainingStatus::Completed),
        priority: None,
        training_type: None,
        date_from: Some(start_date.clone()),
        date_to: Some(end_date.clone()),
        min_cost: None,
        max_cost: None,
    };
    
    let completed_training = service.get_training_needs(Some(filter))?;
    
    // Calculate ROI metrics
    let total_investment = completed_training.iter()
        .map(|need| need.estimated_cost)
        .sum::<f64>();
    
    // Estimated productivity gains (mock calculation - 15% average improvement)
    let productivity_gain_rate = 0.15;
    let avg_salary = 75000.0; // Average employee salary
    let estimated_productivity_gains = completed_training.len() as f64 * avg_salary * productivity_gain_rate;
    
    // Calculate ROI percentage
    let roi_percentage = if total_investment > 0.0 {
        ((estimated_productivity_gains - total_investment) / total_investment) * 100.0
    } else {
        0.0
    };
    
    // Calculate payback period in months
    let monthly_productivity_gain = estimated_productivity_gains / 12.0;
    let payback_period_months = if monthly_productivity_gain > 0.0 {
        total_investment / monthly_productivity_gain
    } else {
        0.0
    };
    
    Ok(RoiAnalysis {
        total_investment,
        realized_benefits: estimated_productivity_gains * 0.6, // 60% of expected gains realized
        projected_benefits: estimated_productivity_gains,
        roi_percentage,
        payback_period_months: payback_period_months as i32,
        by_department: vec![], // TODO: Implement department breakdown
    })
}

// ============= CSV Import/Export Commands =============

#[tauri::command]
pub async fn import_training_needs_csv(
    csv_content: String,
    validate_only: bool,
) -> Result<ImportResult, String> {
    let reader = csv_content.as_bytes();
    CsvImporter::import_training_needs(reader, validate_only)
}

#[tauri::command]
pub async fn export_training_needs_csv(
    state: State<'_, TrainingState>,
    filter: Option<TrainingNeedFilter>,
) -> Result<String, String> {
    let service = state.service.lock().await;
    let needs = service.get_training_needs(filter)?;
    
    let mut writer = Vec::new();
    CsvImporter::export_training_needs(&mut writer, needs)?;
    
    String::from_utf8(writer)
        .map_err(|e| format!("Failed to convert CSV to string: {}", e))
}

#[tauri::command]
pub async fn generate_csv_template() -> Result<String, String> {
    let mut writer = Vec::new();
    CsvImporter::generate_template(&mut writer)?;
    
    String::from_utf8(writer)
        .map_err(|e| format!("Failed to convert template to string: {}", e))
}

// ============= Batch Operations Commands =============

#[tauri::command]
pub async fn batch_create_training_needs(
    state: State<'_, TrainingState>,
    requests: Vec<CreateTrainingNeedRequest>,
) -> Result<BatchOperationResult, String> {
    let service = state.service.lock().await;
    let mut results = BatchOperationResult {
        total: requests.len(),
        successful: 0,
        failed: 0,
        errors: Vec::new(),
    };
    
    for (index, request) in requests.into_iter().enumerate() {
        match service.create_training_need(request) {
            Ok(_) => results.successful += 1,
            Err(e) => {
                results.failed += 1;
                results.errors.push(BatchError {
                    index,
                    message: e,
                });
            }
        }
    }
    
    Ok(results)
}

#[tauri::command]
pub async fn batch_update_status(
    training_need_ids: Vec<i64>,
    _new_status: TrainingStatus,
) -> Result<BatchOperationResult, String> {
    let results = BatchOperationResult {
        total: training_need_ids.len(),
        successful: 0,
        failed: 0,
        errors: Vec::new(),
    };
    
    // TODO: Implement batch status update
    
    Ok(results)
}

// ============= Search and Filter Commands =============

#[tauri::command]
pub async fn search_training_needs(
    state: State<'_, TrainingState>,
    query: String,
    search_fields: Vec<String>,
) -> Result<Vec<TrainingNeed>, String> {
    let service = state.service.lock().await;
    
    // Get all training needs
    let all_needs = service.get_training_needs(None)?;
    
    // Perform case-insensitive search
    let query_lower = query.to_lowercase();
    
    let results: Vec<TrainingNeed> = all_needs.into_iter()
        .filter(|need| {
            // Search in specified fields
            for field in &search_fields {
                let found = match field.as_str() {
                    "employee_name" => need.employee_name.to_lowercase().contains(&query_lower),
                    "department" => need.department.to_lowercase().contains(&query_lower),
                    "position" => need.position.to_lowercase().contains(&query_lower),
                    "recommended_training" => need.recommended_training.iter()
                        .any(|training| training.to_lowercase().contains(&query_lower)),
                    "notes" => need.notes.as_ref()
                        .map_or(false, |notes| notes.to_lowercase().contains(&query_lower)),
                    _ => false,
                };
                
                if found {
                    return true;
                }
            }
            
            // If no specific fields specified, search in all text fields
            if search_fields.is_empty() {
                return need.employee_name.to_lowercase().contains(&query_lower) ||
                       need.department.to_lowercase().contains(&query_lower) ||
                       need.position.to_lowercase().contains(&query_lower) ||
                       need.recommended_training.iter().any(|training| training.to_lowercase().contains(&query_lower)) ||
                       need.notes.as_ref().map_or(false, |notes| notes.to_lowercase().contains(&query_lower));
            }
            
            false
        })
        .collect();
    
    Ok(results)
}

#[tauri::command]
pub async fn get_training_suggestions(
    state: State<'_, TrainingState>,
    employee_id: String,
    max_results: Option<usize>,
) -> Result<Vec<TrainingSuggestion>, String> {
    let service = state.service.lock().await;
    
    // Get employee's existing training needs
    let filter = TrainingNeedFilter {
        department: None,
        status: None,
        priority: None,
        training_type: None,
        date_from: None,
        date_to: None,
        min_cost: None,
        max_cost: None,
    };
    
    let all_needs = service.get_training_needs(Some(filter))?;
    let employee_needs: Vec<_> = all_needs.iter()
        .filter(|need| need.employee_name.contains(&employee_id) || need.employee_id == employee_id)
        .collect();
    
    // Analyze skill gaps and generate suggestions
    let mut suggestions = Vec::new();
    
    // Common skill areas that complement existing training
    let skill_suggestions = vec![
        ("Leadership", "Develop management and team leadership skills"),
        ("Communication", "Improve presentation and interpersonal communication"),
        ("Technical Skills", "Stay updated with latest technology trends"),
        ("Project Management", "Learn agile methodologies and project planning"),
        ("Data Analysis", "Enhance analytical and decision-making capabilities"),
        ("Digital Literacy", "Improve proficiency with digital tools and platforms"),
        ("Customer Service", "Enhance customer interaction and service quality"),
        ("Problem Solving", "Develop critical thinking and problem-solving abilities"),
    ];
    
    // Generate suggestions based on priority and relevance
    for (index, (skill, description)) in skill_suggestions.iter().enumerate() {
        if suggestions.len() >= max_results.unwrap_or(8) {
            break;
        }
        
        // Check if employee already has training in this area
        let already_has_training = employee_needs.iter()
            .any(|need| need.recommended_training.iter()
                .any(|training| training.to_lowercase().contains(&skill.to_lowercase())));
        
        if !already_has_training {
            suggestions.push(TrainingSuggestion {
                program_id: Some((index + 1) as i64),
                program_name: format!("{} Development Program", skill),
                reason: format!("Based on career development path and skill gap analysis for {}: {}", skill, description),
                relevance_score: 0.8 - (index as f64 * 0.05),
                estimated_impact: if index < 3 { "High".to_string() } else { "Medium".to_string() },
            });
        }
    }
    
    // Sort by relevance score and estimated impact
    suggestions.sort_by(|a, b| {
        let impact_order = |impact: &str| match impact {
            "High" => 3,
            "Medium" => 2,
            "Low" => 1,
            _ => 0,
        };
        
        impact_order(&b.estimated_impact).cmp(&impact_order(&a.estimated_impact))
            .then(b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal))
    });
    
    Ok(suggestions)
}

// ============= Notification Commands =============

#[tauri::command]
pub async fn send_training_reminder(
    _training_need_id: i64,
    _recipient_email: String,
) -> Result<bool, String> {
    // TODO: Implement email notification
    Ok(true)
}

#[tauri::command]
pub async fn schedule_training_notifications(
    _training_need_ids: Vec<i64>,
    _notification_type: NotificationType,
) -> Result<bool, String> {
    // TODO: Implement scheduled notifications
    Ok(true)
}

// ============= Supporting Structures =============

#[derive(Debug, Serialize, Deserialize)]
pub struct RoiAnalysis {
    pub total_investment: f64,
    pub realized_benefits: f64,
    pub projected_benefits: f64,
    pub roi_percentage: f64,
    pub payback_period_months: i32,
    pub by_department: Vec<DepartmentRoi>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DepartmentRoi {
    pub department: String,
    pub investment: f64,
    pub benefits: f64,
    pub roi: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchOperationResult {
    pub total: usize,
    pub successful: usize,
    pub failed: usize,
    pub errors: Vec<BatchError>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchError {
    pub index: usize,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrainingSuggestion {
    pub program_id: Option<i64>,
    pub program_name: String,
    pub reason: String,
    pub relevance_score: f64,
    pub estimated_impact: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum NotificationType {
    StartReminder,
    DeadlineReminder,
    CompletionCertificate,
    ManagerApproval,
    ProgressUpdate,
}

// Function to register all training commands with Tauri
pub fn register_training_commands() -> Vec<&'static str> {
    vec![
        "create_training_need_enhanced",
        "get_training_needs_enhanced",
        "update_training_need_enhanced",
        "delete_training_need_enhanced",
        "analyze_skills_gap",
        "get_skill_recommendations",
        "create_training_program_enhanced",
        "get_training_programs_enhanced",
        "match_programs_to_needs",
        "create_learning_path",
        "update_module_progress",
        "get_learning_paths",
        "create_approval_workflow",
        "process_approval",
        "get_pending_approvals",
        "get_training_metrics",
        "get_department_metrics",
        "get_roi_analysis",
        "import_training_needs_csv",
        "export_training_needs_csv",
        "generate_csv_template",
        "batch_create_training_needs",
        "batch_update_status",
        "search_training_needs",
        "get_training_suggestions",
        "send_training_reminder",
        "schedule_training_notifications",
    ]
}