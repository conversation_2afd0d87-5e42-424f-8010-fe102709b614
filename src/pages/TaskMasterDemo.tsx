import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TaskMasterWidget } from "@/components/widgets/TaskMasterWidget";
import { TaskMasterPanel } from "@/components/taskmaster/TaskMasterPanel";
import { useTaskMasterDemo } from "@/hooks/useTaskMaster";
import { FileEdit, LayoutGrid, Rocket } from "lucide-react";

export const TaskMasterDemo: React.FC = () => {
  const [showFullPanel, setShowFullPanel] = useState(false);
  const [activeTab, setActiveTab] = useState('widget');
  const { loadDemoData } = useTaskMasterDemo();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="max-w-4xl mx-auto">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">Task Master Demo</h1>
              <p className="text-muted-foreground">
                Advanced task management system for Claudia
              </p>
            </div>
            <Button onClick={loadDemoData} variant="outline">
              <Rocket className="h-4 w-4 mr-2" />
              Load Demo Data
            </Button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="widget">Widget View</TabsTrigger>
              <TabsTrigger value="integration">Integration Example</TabsTrigger>
            </TabsList>

            <TabsContent value="widget" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card className="p-4">
                  <h3 className="font-semibold mb-3">Compact Widget</h3>
                  <TaskMasterWidget compact />
                </Card>

                <Card className="p-4">
                  <h3 className="font-semibold mb-3">Full Widget</h3>
                  <TaskMasterWidget 
                    onTaskClick={(taskId) => {
                      console.log('Task clicked:', taskId);
                      setShowFullPanel(true);
                    }}
                    onCreateTask={() => {
                      console.log('Create task clicked');
                      setShowFullPanel(true);
                    }}
                  />
                </Card>
              </div>

              <Card className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Full Task Master Panel</h3>
                  <Button onClick={() => setShowFullPanel(true)}>
                    <LayoutGrid className="h-4 w-4 mr-2" />
                    Open Task Master
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Click the button above to open the full Task Master interface with all features
                </p>
              </Card>
            </TabsContent>

            <TabsContent value="integration" className="space-y-4">
              <Card className="p-4">
                <h3 className="font-semibold mb-3">Integration Code Example</h3>
                <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                  <code className="text-sm">{`import { TaskMasterWidget } from "@/components/widgets/TaskMasterWidget";
import { TaskMasterPanel } from "@/components/taskmaster/TaskMasterPanel";
import { useTaskMaster } from "@/hooks/useTaskMaster";

// Example 1: Simple widget in your component
function MyComponent() {
  return (
    <TaskMasterWidget 
      compact={false}
      onTaskClick={(taskId) => console.log('Task:', taskId)}
    />
  );
}

// Example 2: Using the Task Master store directly
function MyTaskManager() {
  const { tasks, createTask, updateTask } = useTaskMaster();
  
  const handleCreateTask = () => {
    createTask({
      title: "New Task",
      description: "Task description",
      status: "pending",
      priority: "medium",
      dependencies: []
    });
  };
  
  return (
    <div>
      {tasks.map(task => (
        <div key={task.id}>{task.title}</div>
      ))}
      <button onClick={handleCreateTask}>Add Task</button>
    </div>
  );
}

// Example 3: Loading task data from JSON
import taskData from './tasks.json';

function MyProject() {
  const { loadTaskData } = useTaskMaster(taskData);
  
  return <TaskMasterPanel />;
}`}</code>
                </pre>
              </Card>

              <Card className="p-4">
                <h3 className="font-semibold mb-3">Features</h3>
                <div className="grid gap-2 text-sm">
                  <div className="flex items-start gap-2">
                    <FileEdit className="h-4 w-4 mt-0.5 text-primary" />
                    <div>
                      <strong>Full Task Management:</strong> Create, edit, delete tasks with subtasks, dependencies, and milestones
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <LayoutGrid className="h-4 w-4 mt-0.5 text-primary" />
                    <div>
                      <strong>Multiple Views:</strong> List, Kanban, Timeline, and Calendar views
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <FileEdit className="h-4 w-4 mt-0.5 text-primary" />
                    <div>
                      <strong>Advanced Features:</strong> Filtering, sorting, bulk operations, and analytics
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <h3 className="font-semibold mb-3">Task Data Structure</h3>
                <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                  <code className="text-sm">{`{
  "project": {
    "name": "My Project",
    "description": "Project description",
    "version": "1.0.0",
    "created": "2024-01-31",
    "estimatedDuration": "4 weeks"
  },
  "tasks": [
    {
      "id": "task-1",
      "title": "Task Title",
      "description": "Task description",
      "status": "pending", // pending | in_progress | completed | blocked | cancelled
      "priority": "medium", // critical | high | medium | low
      "estimatedHours": 8,
      "assignee": "John Doe",
      "dependencies": [], // Array of task IDs
      "subtasks": [
        {
          "id": "subtask-1",
          "title": "Subtask Title",
          "description": "Subtask description",
          "status": "pending",
          "estimatedHours": 2
        }
      ],
      "tags": ["frontend", "urgent"],
      "dueDate": "2024-02-15",
      "createdAt": "2024-01-31T10:00:00Z",
      "updatedAt": "2024-01-31T10:00:00Z"
    }
  ],
  "milestones": [
    {
      "id": "milestone-1",
      "title": "MVP Release",
      "targetDate": "2024-02-28",
      "tasks": ["task-1", "task-2"],
      "status": "pending"
    }
  ]
}`}</code>
                </pre>
              </Card>
            </TabsContent>
          </Tabs>
        </Card>
      </div>

      {/* Full Panel Modal */}
      {showFullPanel && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed inset-4 z-50 bg-background border rounded-lg shadow-lg overflow-hidden">
            <TaskMasterPanel onClose={() => setShowFullPanel(false)} />
          </div>
        </div>
      )}
    </div>
  );
};