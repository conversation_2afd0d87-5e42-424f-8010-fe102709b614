import React, { useState, useEffect } from "react";
import { 
  Bar<PERSON>hart3, 
  DollarSign, 
  Clock, 
  Zap,
  Download,
  RefreshCw,
  Info
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface UsageMetrics {
  totalTokens: number;
  totalCost: number;
  totalSessions: number;
  totalTime: number;
  modelBreakdown: {
    model: string;
    tokens: number;
    cost: number;
    sessions: number;
  }[];
  dailyUsage: {
    date: string;
    tokens: number;
    cost: number;
    sessions: number;
  }[];
}

interface UsageDashboardProps {
  projectPath?: string;
  className?: string;
}

/**
 * UsageDashboard component for tracking Claude Code usage and costs
 */
export const UsageDashboard: React.FC<UsageDashboardProps> = ({
  projectPath,
  className
}) => {
  const [metrics, setMetrics] = useState<UsageMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "all">("30d");
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('models');

  useEffect(() => {
    loadMetrics();
  }, [projectPath, timeRange]);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call when backend support is added
      // const data = await api.getUsageMetrics(projectPath, timeRange);
      
      // Mock data for now
      const mockData: UsageMetrics = {
        totalTokens: 1234567,
        totalCost: 45.67,
        totalSessions: 89,
        totalTime: 3456,
        modelBreakdown: [
          { model: "opus-4.1", tokens: 567890, cost: 25.43, sessions: 34 },
          { model: "sonnet", tokens: 456789, cost: 15.24, sessions: 55 }
        ],
        dailyUsage: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
          tokens: Math.floor(Math.random() * 50000),
          cost: Math.random() * 5,
          sessions: Math.floor(Math.random() * 10)
        })).reverse()
      };
      
      setMetrics(mockData);
    } catch (error) {
      console.error("Failed to load usage metrics:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMetrics();
    setRefreshing(false);
  };

  const handleExport = () => {
    if (!metrics) return;
    
    const csv = [
      ["Date", "Tokens", "Cost", "Sessions"],
      ...metrics.dailyUsage.map(d => [
        d.date,
        d.tokens.toString(),
        d.cost.toFixed(2),
        d.sessions.toString()
      ])
    ].map(row => row.join(",")).join("\n");
    
    const blob = new Blob([csv], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `claude-usage-${timeRange}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD"
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-muted-foreground">No usage data available</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Usage Analytics</h2>
          <p className="text-muted-foreground">
            Track your Claude Code usage and costs
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={(v: any) => setTimeRange(v)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(metrics.totalTokens)}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.totalCost)}</div>
            <p className="text-xs text-muted-foreground">
              +8% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sessions</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalSessions}</div>
            <p className="text-xs text-muted-foreground">
              +23% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDuration(metrics.totalTime)}</div>
            <p className="text-xs text-muted-foreground">
              Avg {Math.round(metrics.totalTime / metrics.totalSessions)}m per session
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="models">Model Usage</TabsTrigger>
          <TabsTrigger value="daily">Daily Trends</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
        </TabsList>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage by Model</CardTitle>
              <CardDescription>
                Token usage and costs breakdown by model
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {metrics.modelBreakdown.map((model) => (
                <div key={model.model} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant={model.model === "opus-4.1" ? "default" : "secondary"}>
                        {model.model === "opus-4.1" ? "Opus 4.1" :
                         model.model === "opus" ? "Claude 4 Opus" : "Claude 4 Sonnet"}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {model.sessions} sessions
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(model.cost)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatNumber(model.tokens)} tokens
                      </p>
                    </div>
                  </div>
                  <Progress 
                    value={(model.tokens / metrics.totalTokens) * 100}
                    className="h-2"
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Usage Trends</CardTitle>
              <CardDescription>
                Usage patterns over the selected time period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metrics.dailyUsage.slice(-7).map((day) => (
                  <div key={day.date} className="flex items-center justify-between py-2 border-b last:border-0">
                    <div>
                      <p className="font-medium">
                        {new Date(day.date).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {day.sessions} sessions
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(day.cost)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatNumber(day.tokens)} tokens
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Usage</CardTitle>
              <CardDescription>
                Usage breakdown by project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Info className="h-8 w-8 mx-auto mb-2" />
                <p>Project-level analytics coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};