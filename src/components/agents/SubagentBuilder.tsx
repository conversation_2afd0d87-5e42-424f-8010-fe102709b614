import React, { useState, useEffect } from "react";
import {
  Bot,
  Plus,
  Save,
  FileText,
  Settings,
  Layers,
  Download,
  Upload,
  CheckCircle,
  Search,
  Terminal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { To<PERSON>, ToastContainer } from "@/components/ui/toast";
import { type Agent } from "@/lib/api";
import { cn } from "@/lib/utils";

interface SubagentConfig {
  name: string;
  type: "general-purpose" | "code-reviewer" | "debugger" | "test-writer" | "docs-writer" | "custom";
  description: string;
  capabilities: string[];
  tools: string[];
  systemPrompt: string;
  model: "sonnet" | "opus-4.1";
  maxTokens?: number;
  temperature?: number;
  command?: string;
  defaultTask?: string;
  hooks?: {
    preExecution?: string;
    postExecution?: string;
  };
}

interface SubagentBuilderProps {
  /**
   * Existing agent to edit (if editing)
   */
  agent?: Agent;
  /**
   * Callback when subagent is saved
   */
  onSave?: (config: SubagentConfig) => void;
  /**
   * Callback to close the builder
   */
  onClose?: () => void;
  /**
   * Optional className for styling
   */
  className?: string;
}

const SUBAGENT_TYPES = [
  {
    type: "general-purpose",
    name: "General Purpose",
    icon: Bot,
    description: "Versatile agent for various tasks",
    defaultTools: ["Read", "Write", "Edit", "Grep", "WebSearch"],
    defaultPrompt: "You are a helpful AI assistant that can handle a wide variety of tasks."
  },
  {
    type: "code-reviewer",
    name: "Code Reviewer",
    icon: Search,
    description: "Specialized in code review and best practices",
    defaultTools: ["Read", "Grep", "WebSearch"],
    defaultPrompt: "You are an expert code reviewer. Analyze code for best practices, potential bugs, performance issues, and security vulnerabilities."
  },
  {
    type: "debugger",
    name: "Debugger",
    icon: Terminal,
    description: "Expert at finding and fixing bugs",
    defaultTools: ["Read", "Edit", "MultiEdit", "Grep", "Bash"],
    defaultPrompt: "You are a debugging expert. Identify and fix bugs, analyze error messages, and improve code reliability."
  },
  {
    type: "test-writer",
    name: "Test Writer",
    icon: CheckCircle,
    description: "Creates comprehensive test suites",
    defaultTools: ["Read", "Write", "Edit", "Bash"],
    defaultPrompt: "You are a testing expert. Write comprehensive unit tests, integration tests, and end-to-end tests with good coverage."
  },
  {
    type: "docs-writer",
    name: "Documentation Writer",
    icon: FileText,
    description: "Creates clear documentation",
    defaultTools: ["Read", "Write", "Edit", "WebSearch"],
    defaultPrompt: "You are a technical documentation expert. Create clear, comprehensive documentation including API docs, guides, and READMEs."
  },
  {
    type: "custom",
    name: "Custom",
    icon: Settings,
    description: "Build your own specialized agent",
    defaultTools: [],
    defaultPrompt: ""
  }
];

const AVAILABLE_TOOLS = [
  "Read", "Write", "Edit", "MultiEdit", "Grep", "Glob", "LS",
  "Bash", "WebSearch", "WebFetch", "TodoWrite", "Task",
  "NotebookEdit", "BashOutput", "KillBash", "ExitPlanMode"
];

const CAPABILITY_SUGGESTIONS = [
  "Code analysis and review",
  "Bug detection and fixing",
  "Performance optimization",
  "Security vulnerability scanning",
  "Test generation",
  "Documentation creation",
  "Refactoring suggestions",
  "API design",
  "Database optimization",
  "Cloud architecture",
  "CI/CD pipeline setup",
  "Dependency management"
];

/**
 * SubagentBuilder component for creating custom subagents
 * Provides a visual interface for configuring specialized AI agents
 */
export const SubagentBuilder: React.FC<SubagentBuilderProps> = ({
  agent,
  onSave,
  onClose,
  className
}) => {
  const [config, setConfig] = useState<SubagentConfig>({
    name: "",
    type: "general-purpose",
    description: "",
    capabilities: [],
    tools: SUBAGENT_TYPES[0].defaultTools,
    systemPrompt: SUBAGENT_TYPES[0].defaultPrompt,
    model: "sonnet",
    maxTokens: 4096,
    temperature: 0.7
  });
  
  const [activeTab, setActiveTab] = useState("basic");
  const [saving, setSaving] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [importJson, setImportJson] = useState("");
  const [newCapability, setNewCapability] = useState("");

  // Load agent data if editing
  useEffect(() => {
    if (agent) {
      setConfig({
        name: agent.name,
        type: "custom",
        description: agent.default_task || "",
        capabilities: [],
        tools: [],
        systemPrompt: agent.system_prompt,
        model: (agent.model as "sonnet" | "opus-4.1") || "sonnet"
      });
    }
  }, [agent]);

  const handleTypeChange = (type: SubagentConfig["type"]) => {
    const typeConfig = SUBAGENT_TYPES.find(t => t.type === type);
    if (typeConfig) {
      setConfig(prev => ({
        ...prev,
        type,
        tools: typeConfig.defaultTools,
        systemPrompt: typeConfig.defaultPrompt || prev.systemPrompt
      }));
    }
  };

  const handleToolToggle = (tool: string) => {
    setConfig(prev => ({
      ...prev,
      tools: prev.tools.includes(tool)
        ? prev.tools.filter(t => t !== tool)
        : [...prev.tools, tool]
    }));
  };

  const handleCapabilityAdd = () => {
    if (newCapability.trim() && !config.capabilities.includes(newCapability)) {
      setConfig(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, newCapability.trim()]
      }));
      setNewCapability("");
    }
  };

  const handleCapabilityRemove = (capability: string) => {
    setConfig(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  const handleSave = async () => {
    if (!config.name || !config.systemPrompt) {
      setToast({ message: "Name and system prompt are required", type: "error" });
      return;
    }

    try {
      setSaving(true);
      // Save the subagent configuration
      onSave?.(config);
      setToast({ message: "Subagent saved successfully", type: "success" });
      setTimeout(() => onClose?.(), 1500);
    } catch (error) {
      console.error("Failed to save subagent:", error);
      setToast({ message: "Failed to save subagent", type: "error" });
    } finally {
      setSaving(false);
    }
  };

  const handleExport = () => {
    const exportData = JSON.stringify(config, null, 2);
    const blob = new Blob([exportData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${config.name || "subagent"}.json`;
    a.click();
    URL.revokeObjectURL(url);
    setToast({ message: "Configuration exported", type: "success" });
  };

  const handleImport = () => {
    try {
      const imported = JSON.parse(importJson);
      setConfig(imported);
      setShowImportDialog(false);
      setImportJson("");
      setToast({ message: "Configuration imported", type: "success" });
    } catch (error) {
      setToast({ message: "Invalid JSON format", type: "error" });
    }
  };

  const getTypeIcon = (type: SubagentConfig["type"]) => {
    const typeConfig = SUBAGENT_TYPES.find(t => t.type === type);
    return typeConfig?.icon || Bot;
  };

  return (
    <>
      <div className={cn("max-w-4xl mx-auto space-y-6", className)}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Subagent Builder
            </CardTitle>
            <CardDescription>
              Create specialized AI agents for specific tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                {/* Agent Type Selection */}
                <div className="space-y-2">
                  <Label>Agent Type</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {SUBAGENT_TYPES.map((type) => {
                      const Icon = type.icon;
                      return (
                        <Card
                          key={type.type}
                          className={cn(
                            "cursor-pointer transition-all hover:shadow-md",
                            config.type === type.type && "ring-2 ring-primary"
                          )}
                          onClick={() => handleTypeChange(type.type as SubagentConfig["type"])}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start gap-3">
                              <Icon className="h-5 w-5 text-primary mt-0.5" />
                              <div className="flex-1">
                                <h4 className="font-medium text-sm">{type.name}</h4>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {type.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>

                {/* Name Input */}
                <div className="space-y-2">
                  <Label htmlFor="name">Agent Name</Label>
                  <Input
                    id="name"
                    value={config.name}
                    onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Code Quality Analyzer"
                  />
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={config.description}
                    onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this agent does..."
                    rows={3}
                  />
                </div>

                {/* Model Selection */}
                <div className="space-y-2">
                  <Label>Model</Label>
                  <Select 
                    value={config.model} 
                    onValueChange={(value: SubagentConfig["model"]) => 
                      setConfig(prev => ({ ...prev, model: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sonnet">Claude 4 Sonnet</SelectItem>
                      <SelectItem value="opus-4.1">
                        <div className="flex items-center gap-2">
                          <span>Opus 4.1</span>
                          <Badge variant="secondary" className="text-xs">NEW</Badge>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="capabilities" className="space-y-4">
                {/* Capabilities */}
                <div className="space-y-2">
                  <Label>Capabilities</Label>
                  <div className="flex gap-2">
                    <Input
                      value={newCapability}
                      onChange={(e) => setNewCapability(e.target.value)}
                      placeholder="Add a capability..."
                      onKeyPress={(e) => e.key === "Enter" && handleCapabilityAdd()}
                    />
                    <Button onClick={handleCapabilityAdd} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Capability Suggestions */}
                  <div className="flex flex-wrap gap-2">
                    {CAPABILITY_SUGGESTIONS.filter(s => !config.capabilities.includes(s)).slice(0, 5).map(suggestion => (
                      <Badge
                        key={suggestion}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                        onClick={() => {
                          setNewCapability(suggestion);
                          handleCapabilityAdd();
                        }}
                      >
                        {suggestion}
                      </Badge>
                    ))}
                  </div>

                  {/* Selected Capabilities */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {config.capabilities.map(capability => (
                      <Badge
                        key={capability}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => handleCapabilityRemove(capability)}
                      >
                        {capability}
                        <span className="ml-1">×</span>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Tools Selection */}
                <div className="space-y-2">
                  <Label>Available Tools</Label>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                    {AVAILABLE_TOOLS.map(tool => (
                      <div
                        key={tool}
                        className={cn(
                          "flex items-center gap-2 p-2 rounded border cursor-pointer transition-all",
                          config.tools.includes(tool) 
                            ? "bg-primary/10 border-primary" 
                            : "hover:bg-muted"
                        )}
                        onClick={() => handleToolToggle(tool)}
                      >
                        <div className={cn(
                          "h-4 w-4 rounded border-2",
                          config.tools.includes(tool) 
                            ? "bg-primary border-primary" 
                            : "border-muted-foreground"
                        )} />
                        <span className="text-sm">{tool}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Prompt */}
                <div className="space-y-2">
                  <Label htmlFor="system-prompt">System Prompt</Label>
                  <Textarea
                    id="system-prompt"
                    value={config.systemPrompt}
                    onChange={(e) => setConfig(prev => ({ ...prev, systemPrompt: e.target.value }))}
                    placeholder="Define the agent's behavior and expertise..."
                    rows={6}
                    className="font-mono text-sm"
                  />
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                {/* Max Tokens */}
                <div className="space-y-2">
                  <Label htmlFor="max-tokens">Max Tokens</Label>
                  <Input
                    id="max-tokens"
                    type="number"
                    value={config.maxTokens}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      maxTokens: parseInt(e.target.value) || 4096 
                    }))}
                    min={100}
                    max={32000}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum number of tokens per response (100-32000)
                  </p>
                </div>

                {/* Temperature */}
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature</Label>
                  <Input
                    id="temperature"
                    type="number"
                    value={config.temperature}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      temperature: parseFloat(e.target.value) || 0.7 
                    }))}
                    min={0}
                    max={1}
                    step={0.1}
                  />
                  <p className="text-xs text-muted-foreground">
                    Controls randomness (0 = focused, 1 = creative)
                  </p>
                </div>

                {/* Hooks */}
                <div className="space-y-4">
                  <Label>Execution Hooks</Label>
                  
                  <div className="space-y-2">
                    <Label htmlFor="pre-hook" className="text-sm">Pre-execution Hook</Label>
                    <Textarea
                      id="pre-hook"
                      value={config.hooks?.preExecution || ""}
                      onChange={(e) => setConfig(prev => ({ 
                        ...prev, 
                        hooks: { ...prev.hooks, preExecution: e.target.value }
                      }))}
                      placeholder="Commands to run before agent execution..."
                      rows={3}
                      className="font-mono text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="post-hook" className="text-sm">Post-execution Hook</Label>
                    <Textarea
                      id="post-hook"
                      value={config.hooks?.postExecution || ""}
                      onChange={(e) => setConfig(prev => ({ 
                        ...prev, 
                        hooks: { ...prev.hooks, postExecution: e.target.value }
                      }))}
                      placeholder="Commands to run after agent execution..."
                      rows={3}
                      className="font-mono text-sm"
                    />
                  </div>
                </div>

                {/* Import/Export */}
                <div className="flex gap-3">
                  <Button 
                    variant="outline" 
                    onClick={handleExport}
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Config
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowImportDialog(true)}
                    className="flex-1"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import Config
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {React.createElement(getTypeIcon(config.type), { className: "h-5 w-5" })}
                      {config.name || "Unnamed Agent"}
                    </CardTitle>
                    <CardDescription>
                      {config.description || "No description provided"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Model</Label>
                      <Badge variant="secondary" className="mt-1">
                        {config.model === "opus-4.1" ? "Opus 4.1" : "Claude 4 Sonnet"}
                      </Badge>
                    </div>

                    {config.capabilities.length > 0 && (
                      <div>
                        <Label>Capabilities</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {config.capabilities.map(cap => (
                            <Badge key={cap} variant="outline">
                              {cap}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <Label>Available Tools</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {config.tools.map(tool => (
                          <Badge key={tool} variant="secondary">
                            {tool}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>System Prompt</Label>
                      <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-x-auto">
                        {config.systemPrompt || "No system prompt defined"}
                      </pre>
                    </div>

                    {config.maxTokens && (
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Max Tokens: {config.maxTokens}</span>
                        <span>Temperature: {config.temperature}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-6">
              <Button
                onClick={handleSave}
                disabled={saving || !config.name || !config.systemPrompt}
                className="flex-1"
              >
                {saving ? (
                  <>Saving...</>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Subagent
                  </>
                )}
              </Button>
              {onClose && (
                <Button onClick={onClose} variant="outline">
                  Cancel
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Configuration</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              value={importJson}
              onChange={(e) => setImportJson(e.target.value)}
              placeholder="Paste JSON configuration here..."
              rows={10}
              className="font-mono text-sm"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImportDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleImport}>
              Import
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {toast && (
        <ToastContainer>
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={() => setToast(null)}
          />
        </ToastContainer>
      )}
    </>
  );
};