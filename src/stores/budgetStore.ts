import { create } from 'zustand'
import { invoke } from '@tauri-apps/api/core'

// Base types
export type Budget = { 
  id: number; 
  year: number; 
  total_amount: number; 
  spent_amount: number; 
  created_at: string; 
  updated_at: string;
}

export type CategoryTotal = { 
  category: string; 
  total: number;
}

export type Stats = { 
  year: number; 
  total: number; 
  spent: number; 
  remaining: number; 
  count_pending: number; 
  count_approved: number; 
  count_rejected: number; 
  by_category: CategoryTotal[];
}

export type ExpenseStatus = 'draft' | 'submitted' | 'paid' | 'reimbursed';

export type Expense = {
  id: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  status: ExpenseStatus;
  date: string;
  created_at: string;
  updated_at: string;
  department_id?: string;
  recurrence?: ExpenseRecurrence;
  receipt_url?: string;
}

export type ExpenseRecurrence = 'none' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

export type CategoryLimit = {
  id: string;
  category: string;
  limit: number;
  spent: number;
  year: number;
}

export type DepartmentAllocation = {
  id: string;
  name: string;
  amount: number;
  spent: number;
  year: number;
}

export type QuarterlyAllocation = {
  quarter: number;
  allocated: number;
  spent: number;
  year: number;
}

// Store state
type State = { 
  budget: Budget | null;
  stats: Stats | null;
  loading: boolean;
  year: number;
  expenses: Expense[];
  expensesLoading: boolean;
}

// Store actions
type Actions = {
  setYear: (y: number) => void;
  fetchBudget: (y?: number) => Promise<void>;
  setBudget: (total: number, y?: number) => Promise<void>;
  fetchStats: (y?: number) => Promise<void>;
  
  // Expense actions
  fetchExpenses: (y?: number) => Promise<void>;
  addExpense: (expense: Omit<Expense, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateExpense: (id: string, updates: Partial<Expense>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
}

export const useBudgetStore = create<State & Actions>((set, get) => ({
  // Initial state
  budget: null,
  stats: null,
  loading: false,
  year: new Date().getFullYear(),
  expenses: [],
  expensesLoading: false,
  
  // Existing actions
  setYear: (y) => set({ year: y }),
  
  fetchBudget: async (y) => {
    const year = y ?? get().year
    set({ loading: true })
    try {
      const res = await invoke<Budget>('get_budget', { year })
      set({ budget: res })
    } catch (error) {
      console.warn('get_budget error:', error)
      // Set a default budget for demo purposes
      set({ 
        budget: {
          id: 1,
          year: year,
          total_amount: 100000,
          spent_amount: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      })
    } finally {
      set({ loading: false })
    }
  },
  
  setBudget: async (total, y) => {
    const year = y ?? get().year
    set({ loading: true })
    try {
      const res = await invoke<Budget>('set_budget', { year, totalAmount: total })
      set({ budget: res })
      await get().fetchStats(year)
    } catch (error) {
      console.warn('set_budget error:', error)
      // Update the local budget state
      set({ 
        budget: {
          ...get().budget!,
          total_amount: total,
          updated_at: new Date().toISOString()
        }
      })
    } finally {
      set({ loading: false })
    }
  },
  
  fetchStats: async (y) => {
    const year = y ?? get().year
    try {
      const res = await invoke<Stats>('get_stats', { year })
      set({ stats: res })
    } catch (error) {
      console.warn('get_stats error:', error)
      // Set default stats for demo purposes
      set({
        stats: {
          year: year,
          total: 100000,
          spent: 25000,
          remaining: 75000,
          count_pending: 5,
          count_approved: 10,
          count_rejected: 2,
          by_category: [
            { category: 'Technical Training', total: 30000 },
            { category: 'Leadership Development', total: 25000 },
            { category: 'Certifications', total: 20000 },
            { category: 'Conferences', total: 15000 },
            { category: 'Online Courses', total: 10000 }
          ]
        }
      })
    }
  },
  
  // Expense actions
  fetchExpenses: async (y) => {
    const year = y ?? get().year
    set({ expensesLoading: true })
    try {
      const res = await invoke<Expense[]>('get_expenses', { year })
      set({ expenses: res })
    } catch (error) {
      console.warn('get_expenses not implemented:', error)
      set({ expenses: [] })
    } finally {
      set({ expensesLoading: false })
    }
  },
  
  addExpense: async (expense) => {
    try {
      const res = await invoke<Expense>('add_expense', { expense })
      set({ expenses: [...get().expenses, res] })
      await get().fetchStats()
    } catch (error) {
      console.warn('add_expense error:', error)
      // Add to local state
      const newExpense: Expense = {
        ...expense,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      set({ expenses: [...get().expenses, newExpense] })
    }
  },
  
  updateExpense: async (id, updates) => {
    try {
      await invoke('update_expense', { id, updates })
      await get().fetchExpenses()
      await get().fetchStats()
    } catch (error) {
      console.warn('update_expense error:', error)
      // Update local state
      set({
        expenses: get().expenses.map(e =>
          e.id === id ? { ...e, ...updates, updated_at: new Date().toISOString() } : e
        )
      })
    }
  },
  
  deleteExpense: async (id) => {
    try {
      await invoke('delete_expense', { id })
      set({ expenses: get().expenses.filter(e => e.id !== id) })
      await get().fetchStats()
    } catch (error) {
      console.warn('delete_expense error:', error)
      // Remove from local state
      set({ expenses: get().expenses.filter(e => e.id !== id) })
    }
  },
}))